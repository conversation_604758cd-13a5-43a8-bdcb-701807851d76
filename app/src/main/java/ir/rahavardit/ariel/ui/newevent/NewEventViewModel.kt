package ir.rahavardit.ariel.ui.newevent

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ir.rahavardit.ariel.data.model.Event
import ir.rahavardit.ariel.data.repository.NewEventRepository
import kotlinx.coroutines.launch

/**
 * ViewModel for the new event screen that handles event creation operations.
 */
class NewEventViewModel : ViewModel() {

    private val newEventRepository = NewEventRepository()

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error



    private val _eventCreationResult = MutableLiveData<EventCreationResult>()
    val eventCreationResult: LiveData<EventCreationResult> = _eventCreationResult



    /**
     * Creates a new event.
     *
     * @param token The authentication token.
     * @param title The title of the event.
     * @param date The date of the event.
     */
    fun createEvent(
        token: String,
        title: String,
        date: String
    ) {
        _isLoading.value = true
        _error.value = null

        viewModelScope.launch {
            try {
                val result = newEventRepository.createEvent(token, title, date)

                result.fold(
                    onSuccess = { event ->
                        _eventCreationResult.value = EventCreationResult.Success(event)
                    },
                    onFailure = { exception ->
                        _eventCreationResult.value = EventCreationResult.Error(
                            exception.message ?: "Failed to create event"
                        )
                    }
                )
            } catch (e: Exception) {
                _eventCreationResult.value = EventCreationResult.Error(
                    e.message ?: "An unexpected error occurred"
                )
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Validates the input fields for creating a new event.
     *
     * @param title The title of the event.
     * @param date The date of the event.
     * @return A NewEventValidationResult indicating whether the inputs are valid.
     */
    fun validateInputs(
        title: String,
        date: String?
    ): NewEventValidationResult {
        val isTitleValid = title.isNotBlank()
        val isDateValid = !date.isNullOrBlank()

        return NewEventValidationResult(
            isTitleValid,
            isDateValid
        )
    }

    /**
     * Sealed class representing the result of event creation.
     */
    sealed class EventCreationResult {
        /**
         * Represents a successful event creation.
         *
         * @property event The created event.
         */
        data class Success(val event: Event) : EventCreationResult()

        /**
         * Represents a failed event creation.
         *
         * @property errorMessage The error message.
         */
        data class Error(val errorMessage: String) : EventCreationResult()
    }
}

/**
 * Data class representing the validation result for new event inputs.
 *
 * @property isTitleValid Whether the title is valid.
 * @property isDateValid Whether the date is valid.
 */
data class NewEventValidationResult(
    val isTitleValid: Boolean,
    val isDateValid: Boolean
)
