package ir.rahavardit.ariel.ui.newevent

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.SessionManager
import ir.rahavardit.ariel.databinding.FragmentNewEventBinding
import ir.rahavardit.ariel.ui.components.JalaliDatePickerDialog
import ir.rahavardit.ariel.utils.JalaliDateUtils
import ir.rahavardit.ariel.utils.PersianUtils

/**
 * Fragment for creating a new event.
 */
class NewEventFragment : Fragment() {

    private var _binding: FragmentNewEventBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: NewEventViewModel
    private lateinit var sessionManager: SessionManager

    private var selectedDate: String? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        viewModel = ViewModelProvider(this).get(NewEventViewModel::class.java)
        _binding = FragmentNewEventBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sessionManager = SessionManager(requireContext())

        setupListeners()
        observeViewModel()
    }

    /**
     * Sets up click listeners and other UI interactions.
     */
    private fun setupListeners() {
        // Set up date picker
        binding.etEventDate.setOnClickListener {
            showDatePicker()
        }

        // Set up submit button
        binding.btnSubmit.setOnClickListener {
            val title = binding.etEventTitle.text.toString().trim()
            val date = selectedDate

            val validationResult = viewModel.validateInputs(title, date)

            when {
                !validationResult.isTitleValid -> {
                    binding.tilEventTitle.error = getString(R.string.please_enter_title)
                }
                !validationResult.isDateValid -> {
                    binding.tilEventDate.error = getString(R.string.please_enter_date)
                }
                else -> {
                    // Clear any previous errors
                    binding.tilEventTitle.error = null
                    binding.tilEventDate.error = null

                    // Create event
                    val token = sessionManager.getAuthToken()
                    if (token != null) {
                        viewModel.createEvent(
                            token,
                            title,
                            date!!
                        )
                    } else {
                        showError(getString(R.string.authentication_token_not_found))
                    }
                }
            }
        }
    }

    /**
     * Observes changes in the ViewModel's LiveData.
     */
    private fun observeViewModel() {
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            binding.btnSubmit.isEnabled = !isLoading
        }

        viewModel.error.observe(viewLifecycleOwner) { errorMessage ->
            if (errorMessage != null) {
                showError(errorMessage)
            }
        }

        viewModel.eventCreationResult.observe(viewLifecycleOwner) { result ->
            when (result) {
                is NewEventViewModel.EventCreationResult.Success -> {
                    Toast.makeText(
                        requireContext(),
                        getString(R.string.event_created_successfully),
                        Toast.LENGTH_SHORT
                    ).show()

                    // Clear the form fields
                    clearForm()
                }
                is NewEventViewModel.EventCreationResult.Error -> {
                    showError(result.errorMessage)
                }
            }
        }
    }

    /**
     * Shows the Jalali date picker dialog.
     */
    private fun showDatePicker() {
        val jalaliDatePickerDialog = JalaliDatePickerDialog(
            requireContext(),
            { year, month, day ->
                // Format the selected Jalali date for API (YYYY/MM/DD format with English numerals)
                selectedDate = JalaliDateUtils.formatJalaliDate(year, month, day)

                // Display the date with Persian numerals to the user
                val displayDate = PersianUtils.convertToPersianNumerals(selectedDate!!)
                binding.etEventDate.setText(displayDate)
                binding.tilEventDate.error = null
            }
        )
        jalaliDatePickerDialog.show()
    }

    /**
     * Clears the form fields.
     */
    private fun clearForm() {
        binding.etEventDate.setText("")
        binding.etEventTitle.setText("")
        selectedDate = null
        binding.tilEventDate.error = null
        binding.tilEventTitle.error = null
        binding.tvError.visibility = View.GONE
    }

    /**
     * Shows an error message.
     *
     * @param message The error message to show.
     */
    private fun showError(message: String) {
        binding.tvError.text = message
        binding.tvError.visibility = View.VISIBLE
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
