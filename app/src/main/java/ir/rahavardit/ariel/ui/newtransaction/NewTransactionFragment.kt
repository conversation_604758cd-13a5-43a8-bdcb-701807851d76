package ir.rahavardit.ariel.ui.newtransaction

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.model.BankItem
import ir.rahavardit.ariel.data.model.TagItem
import ir.rahavardit.ariel.data.model.TransactionCategoryItem
import ir.rahavardit.ariel.data.model.TransactionMode
import ir.rahavardit.ariel.databinding.FragmentNewTransactionBinding
import ir.rahavardit.ariel.data.SessionManager
import ir.rahavardit.ariel.ui.components.JalaliDatePickerDialog
import ir.rahavardit.ariel.utils.JalaliDateUtils
import ir.rahavardit.ariel.utils.PersianUtils

/**
 * Fragment for creating a new transaction.
 */
class NewTransactionFragment : Fragment() {

    private var _binding: FragmentNewTransactionBinding? = null
    private val binding get() = _binding!!

    private val viewModel: NewTransactionViewModel by viewModels()
    private lateinit var sessionManager: SessionManager

    private var selectedDate: String? = null
    private var selectedMode: String? = null
    private var selectedBankId: Int? = null
    private var selectedCategoryId: Int? = null
    private val selectedTagIds = mutableListOf<Int>()
    private val selectedTagTitles = mutableListOf<String>()

    private var availableTags: List<TagItem> = emptyList()

    // Edit mode variables
    private var isEditMode = false
    private var editShortUuid: String? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentNewTransactionBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sessionManager = SessionManager(requireContext())

        // Check if we're in edit mode
        checkEditMode()

        setupListeners()
        observeViewModel()
        fetchData()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    /**
     * Checks if we're in edit mode and pre-populates the form.
     */
    private fun checkEditMode() {
        val args = arguments
        if (args != null && args.getString("editMode") == "true") {
            isEditMode = true
            editShortUuid = args.getString("shortUuid")

            // Update UI for edit mode
            binding.btnSubmit.text = "به‌روزرسانی"

            // Pre-populate form with existing data
            populateFormForEdit(args)
        }
    }

    /**
     * Populates the form with existing transaction data for editing.
     */
    private fun populateFormForEdit(args: Bundle) {
        // Set title
        args.getString("title")?.let { title ->
            if (title.isNotEmpty()) {
                binding.etTransactionTitle.setText(title)
            }
        }

        // Set amount
        val amount = args.getInt("amount", 0)
        if (amount > 0) {
            binding.etTransactionAmount.setText(amount.toString())
        }

        // Set date
        val year = args.getInt("year", 0)
        val month = args.getInt("month", 0)
        val day = args.getInt("day", 0)
        if (year > 0 && month > 0 && day > 0) {
            selectedDate = JalaliDateUtils.formatJalaliDate(year, month, day)
            val displayDate = PersianUtils.convertToPersianNumerals(selectedDate!!)
            binding.etTransactionDate.setText(displayDate)
        }

        // Set mode
        selectedMode = args.getString("transactionMode")

        // Set bank
        val bankId = args.getInt("bankId", -1)
        if (bankId != -1) {
            selectedBankId = bankId
        }

        // Set category
        val categoryId = args.getInt("categoryId", -1)
        if (categoryId != -1) {
            selectedCategoryId = categoryId
        }

        // Set tags
        val tagIds = args.getIntegerArrayList("tagIds")
        val tagNames = args.getStringArrayList("tagNames")
        if (tagIds != null && tagNames != null) {
            selectedTagIds.clear()
            selectedTagIds.addAll(tagIds)
            selectedTagTitles.clear()
            selectedTagTitles.addAll(tagNames)
            binding.etTransactionTags.setText(tagNames.joinToString(", "))
        }
    }

    /**
     * Sets up click listeners and other UI interactions.
     */
    private fun setupListeners() {
        // Date picker
        binding.etTransactionDate.setOnClickListener {
            showDatePicker()
        }

        // Tags multi-select
        binding.etTransactionTags.setOnClickListener {
            showTagsDialog()
        }

        // Mode dropdown
        binding.dropdownTransactionMode.setOnItemClickListener { _, _, position, _ ->
            val modes = viewModel.transactionModes.value
            if (modes != null && position < modes.size) {
                selectedMode = modes[position].value
                binding.tilTransactionMode.error = null
            }
        }

        // Bank dropdown
        binding.dropdownTransactionBank.setOnItemClickListener { _, _, position, _ ->
            val banks = viewModel.banks.value
            if (banks != null) {
                if (position == 0) {
                    // Empty option selected
                    selectedBankId = null
                } else if (position - 1 < banks.size) {
                    selectedBankId = banks[position - 1].id
                }
                binding.tilTransactionBank.error = null
            }
        }

        // Category dropdown
        binding.dropdownTransactionCategory.setOnItemClickListener { _, _, position, _ ->
            val categories = viewModel.categories.value
            if (categories != null && position < categories.size) {
                selectedCategoryId = categories[position].id
                binding.tilTransactionCategory.error = null
            }
        }

        // Submit button
        binding.btnSubmit.setOnClickListener {
            val title = binding.etTransactionTitle.text.toString().trim()
            val amount = binding.etTransactionAmount.text.toString().trim()

            val validationResult = viewModel.validateInputs(
                selectedDate,
                amount,
                selectedMode,
                selectedCategoryId?.toString()
            )

            when {
                !validationResult.isDateValid -> {
                    binding.tilTransactionDate.error = getString(R.string.please_select_date)
                }
                !validationResult.isAmountValid -> {
                    binding.tilTransactionAmount.error = getString(R.string.please_enter_amount)
                }
                !validationResult.isModeValid -> {
                    binding.tilTransactionMode.error = getString(R.string.please_select_mode)
                }
                !validationResult.isCategoryValid -> {
                    binding.tilTransactionCategory.error = getString(R.string.please_select_category_transaction)
                }
                else -> {
                    // Clear any previous errors
                    binding.tilTransactionDate.error = null
                    binding.tilTransactionAmount.error = null
                    binding.tilTransactionMode.error = null
                    binding.tilTransactionCategory.error = null

                    // Create or update transaction
                    val token = sessionManager.getAuthToken()
                    if (token != null) {
                        if (isEditMode && editShortUuid != null) {
                            // Update existing transaction
                            viewModel.updateTransaction(
                                token = token,
                                shortUuid = editShortUuid!!,
                                date = selectedDate!!,
                                title = title.ifBlank { null },
                                amount = amount.toInt(),
                                mode = selectedMode!!,
                                bankId = selectedBankId,
                                categoryId = selectedCategoryId!!,
                                tagIds = selectedTagIds.toList()
                            )
                        } else {
                            // Create new transaction
                            viewModel.createTransaction(
                                token = token,
                                date = selectedDate!!,
                                title = title.ifBlank { null },
                                amount = amount.toInt(),
                                mode = selectedMode!!,
                                bankId = selectedBankId,
                                categoryId = selectedCategoryId!!,
                                tagIds = selectedTagIds.toList()
                            )
                        }
                    } else {
                        showError(getString(R.string.authentication_token_not_found))
                    }
                }
            }
        }
    }

    /**
     * Shows the Jalali date picker dialog.
     */
    private fun showDatePicker() {
        val jalaliDatePickerDialog = JalaliDatePickerDialog(
            requireContext(),
            { year, month, day ->
                // Format the selected Jalali date
                selectedDate = JalaliDateUtils.formatJalaliDate(year, month, day)

                // Display the date with Persian numerals
                val displayDate = PersianUtils.convertToPersianNumerals(selectedDate!!)
                binding.etTransactionDate.setText(displayDate)
                binding.tilTransactionDate.error = null
            }
        )
        jalaliDatePickerDialog.show()
    }

    /**
     * Shows the tags selection dialog.
     */
    private fun showTagsDialog() {
        if (availableTags.isEmpty()) {
            Toast.makeText(requireContext(), getString(R.string.error_loading_tags), Toast.LENGTH_SHORT).show()
            return
        }

        val tagTitles = availableTags.map { it.title }.toTypedArray()
        val checkedItems = BooleanArray(tagTitles.size) { index ->
            availableTags[index].id in selectedTagIds
        }

        MaterialAlertDialogBuilder(requireContext())
            .setTitle(getString(R.string.transaction_tags))
            .setMultiChoiceItems(tagTitles, checkedItems) { _, which, isChecked ->
                val tag = availableTags[which]
                if (isChecked) {
                    if (tag.id !in selectedTagIds) {
                        selectedTagIds.add(tag.id)
                        selectedTagTitles.add(tag.title)
                    }
                } else {
                    selectedTagIds.remove(tag.id)
                    selectedTagTitles.remove(tag.title)
                }
            }
            .setPositiveButton(getString(R.string.submit)) { _, _ ->
                binding.etTransactionTags.setText(selectedTagTitles.joinToString(", "))
            }
            .setNegativeButton(getString(R.string.cancel), null)
            .show()
    }

    /**
     * Fetches initial data from the API.
     */
    private fun fetchData() {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.loadTransactionModes(token)
            viewModel.loadBanks(token)
            viewModel.loadCategories(token)
            viewModel.loadTags(token)
        } else {
            showError(getString(R.string.authentication_token_not_found))
        }
    }

    /**
     * Sets up observers for ViewModel LiveData.
     */
    private fun observeViewModel() {
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        }

        viewModel.error.observe(viewLifecycleOwner) { error ->
            error?.let {
                showError(it)
            }
        }

        viewModel.transactionModes.observe(viewLifecycleOwner) { modes ->
            setupModeDropdown(modes)
            // Set selected mode if in edit mode
            if (isEditMode && selectedMode != null) {
                val modeIndex = modes.indexOfFirst { it.value == selectedMode }
                if (modeIndex != -1) {
                    binding.dropdownTransactionMode.setText(modes[modeIndex].label, false)
                }
            }
        }

        viewModel.banks.observe(viewLifecycleOwner) { banks ->
            setupBankDropdown(banks)
            // Set selected bank if in edit mode
            if (isEditMode && selectedBankId != null) {
                val bankIndex = banks.indexOfFirst { it.id == selectedBankId }
                if (bankIndex != -1) {
                    // Add 1 because of empty option at index 0
                    binding.dropdownTransactionBank.setText(banks[bankIndex].title, false)
                }
            }
        }

        viewModel.categories.observe(viewLifecycleOwner) { categories ->
            setupCategoryDropdown(categories)
            // Set selected category if in edit mode
            if (isEditMode && selectedCategoryId != null) {
                val categoryIndex = categories.indexOfFirst { it.id == selectedCategoryId }
                if (categoryIndex != -1) {
                    binding.dropdownTransactionCategory.setText(categories[categoryIndex].title, false)
                }
            }
        }

        viewModel.tags.observe(viewLifecycleOwner) { tags ->
            availableTags = tags
        }

        viewModel.transactionCreationResult.observe(viewLifecycleOwner) { result ->
            when (result) {
                is NewTransactionViewModel.TransactionCreationResult.Success -> {
                    val message = if (isEditMode) {
                        "تراکنش با موفقیت به‌روزرسانی شد"
                    } else {
                        getString(R.string.transaction_created_successfully)
                    }

                    Toast.makeText(
                        requireContext(),
                        message,
                        Toast.LENGTH_SHORT
                    ).show()

                    if (isEditMode) {
                        // Navigate back to home after successful update
                        findNavController().navigate(R.id.nav_home)
                    } else {
                        // Navigate to a fresh new transaction page
                        findNavController().navigate(R.id.nav_new_transaction)
                    }
                }
                is NewTransactionViewModel.TransactionCreationResult.Error -> {
                    showError(result.errorMessage)
                }
            }
        }
    }

    /**
     * Sets up the mode dropdown with available modes.
     */
    private fun setupModeDropdown(modes: List<TransactionMode>) {
        val adapter = ArrayAdapter(
            requireContext(),
            android.R.layout.simple_dropdown_item_1line,
            modes.map { it.label }
        )
        binding.dropdownTransactionMode.setAdapter(adapter)
    }

    /**
     * Sets up the bank dropdown with available banks.
     */
    private fun setupBankDropdown(banks: List<BankItem>) {
        val bankTitles = mutableListOf<String>()
        bankTitles.add("") // Add empty option for optional field
        bankTitles.addAll(banks.map { it.title })

        val adapter = ArrayAdapter(
            requireContext(),
            android.R.layout.simple_dropdown_item_1line,
            bankTitles
        )
        binding.dropdownTransactionBank.setAdapter(adapter)
    }

    /**
     * Sets up the category dropdown with available categories.
     */
    private fun setupCategoryDropdown(categories: List<TransactionCategoryItem>) {
        val adapter = ArrayAdapter(
            requireContext(),
            android.R.layout.simple_dropdown_item_1line,
            categories.map { it.title }
        )
        binding.dropdownTransactionCategory.setAdapter(adapter)
    }

    /**
     * Shows an error message to the user.
     */
    private fun showError(message: String) {
        binding.tvError.text = message
        binding.tvError.visibility = View.VISIBLE

        // Hide error after 5 seconds
        binding.tvError.postDelayed({
            binding.tvError.visibility = View.GONE
        }, 5000)
    }
}
