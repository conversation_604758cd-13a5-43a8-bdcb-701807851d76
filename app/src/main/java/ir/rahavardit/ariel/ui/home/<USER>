package ir.rahavardit.ariel.ui.home

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.PopupMenu
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.model.EventObject

class EventObjectAdapter(
    private var eventList: List<EventObject>,
    private val actionListener: ItemActionListener? = null
) : RecyclerView.Adapter<EventObjectAdapter.EventObjectViewHolder>() {

    class EventObjectViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val textShortUuid: TextView = itemView.findViewById(R.id.text_short_uuid)
        val textTitle: TextView = itemView.findViewById(R.id.text_title)
        val textCreated: TextView = itemView.findViewById(R.id.text_created)
        val btnMenu: ImageButton = itemView.findViewById(R.id.btn_menu)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): EventObjectViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_event_object, parent, false)
        return EventObjectViewHolder(view)
    }

    override fun onBindViewHolder(holder: EventObjectViewHolder, position: Int) {
        val event = eventList[position]

        holder.textShortUuid.text = event.shortUuid
        holder.textTitle.text = event.title
        holder.textCreated.text = event.slashedDatePersian

        // Setup menu button
        holder.btnMenu.setOnClickListener { view ->
            showPopupMenu(view, event)
        }
    }

    private fun showPopupMenu(view: View, event: EventObject) {
        val popupMenu = PopupMenu(view.context, view)
        popupMenu.menuInflater.inflate(R.menu.menu_item_actions, popupMenu.menu)

        popupMenu.setOnMenuItemClickListener { menuItem ->
            when (menuItem.itemId) {
                R.id.action_edit -> {
                    actionListener?.onEditEvent(event)
                    true
                }
                R.id.action_delete -> {
                    actionListener?.onDeleteEvent(event)
                    true
                }
                else -> false
            }
        }

        popupMenu.show()
    }

    override fun getItemCount(): Int = eventList.size

    fun updateData(newEventList: List<EventObject>) {
        eventList = newEventList
        notifyDataSetChanged()
    }
}
