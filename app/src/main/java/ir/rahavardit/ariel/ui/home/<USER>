package ir.rahavardit.ariel.ui.home

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.PopupMenu
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.model.ExpenditureObject
import java.text.NumberFormat
import java.util.Locale

class ExpenditureAdapter(
    private var expenditureList: List<ExpenditureObject>,
    private val actionListener: ItemActionListener? = null
) : RecyclerView.Adapter<ExpenditureAdapter.ExpenditureViewHolder>() {

    class ExpenditureViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val textShortUuid: TextView = itemView.findViewById(R.id.text_short_uuid)
        val textTitle: TextView = itemView.findViewById(R.id.text_title)
        val textAmount: TextView = itemView.findViewById(R.id.text_amount)
        val textBank: TextView = itemView.findViewById(R.id.text_bank)
        val textCategory: TextView = itemView.findViewById(R.id.text_category)
        val textTags: TextView = itemView.findViewById(R.id.text_tags)
        val textCreated: TextView = itemView.findViewById(R.id.text_created)
        val btnMenu: ImageButton = itemView.findViewById(R.id.btn_menu)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ExpenditureViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_expenditure, parent, false)
        return ExpenditureViewHolder(view)
    }

    override fun onBindViewHolder(holder: ExpenditureViewHolder, position: Int) {
        val expenditure = expenditureList[position]

        holder.textShortUuid.text = expenditure.shortUuid
        holder.textBank.text = expenditure.bankInfo.title
        holder.textCreated.text = expenditure.slashedDatePersian

        // Handle title - hide if empty or null
        if (!expenditure.title.isNullOrEmpty()) {
            holder.textTitle.text = expenditure.title
            holder.textTitle.visibility = android.view.View.VISIBLE
        } else {
            holder.textTitle.visibility = android.view.View.GONE
        }

        holder.textCategory.text = "دسته‌بندی: ${expenditure.categoryInfo.title}"

        // Handle tags - hide if empty
        if (expenditure.tagsNames.isNotEmpty()) {
            holder.textTags.text = "برچسب‌ها: ${expenditure.tagsNames.joinToString(", ")}"
            holder.textTags.visibility = android.view.View.VISIBLE
        } else {
            holder.textTags.visibility = android.view.View.GONE
        }

        // Format amount with Persian number formatting
        val numberFormat = NumberFormat.getNumberInstance(Locale("fa", "IR"))
        holder.textAmount.text = "${numberFormat.format(expenditure.amount)} تومان"

        // Setup menu button
        holder.btnMenu.setOnClickListener { view ->
            showPopupMenu(view, expenditure)
        }
    }

    private fun showPopupMenu(view: View, expenditure: ExpenditureObject) {
        val popupMenu = PopupMenu(view.context, view)
        popupMenu.menuInflater.inflate(R.menu.menu_item_actions, popupMenu.menu)

        popupMenu.setOnMenuItemClickListener { menuItem ->
            when (menuItem.itemId) {
                R.id.action_edit -> {
                    actionListener?.onEditExpenditure(expenditure)
                    true
                }
                R.id.action_delete -> {
                    actionListener?.onDeleteExpenditure(expenditure)
                    true
                }
                else -> false
            }
        }

        popupMenu.show()
    }

    override fun getItemCount(): Int = expenditureList.size

    fun updateData(newExpenditureList: List<ExpenditureObject>) {
        expenditureList = newExpenditureList
        notifyDataSetChanged()
    }
}
