package ir.rahavardit.ariel.ui.home

import ir.rahavardit.ariel.data.model.EventObject
import ir.rahavardit.ariel.data.model.ExpenditureObject
import ir.rahavardit.ariel.data.model.IncomeObject

/**
 * Interface for handling item actions (edit/delete) from the homepage adapters.
 */
interface ItemActionListener {
    
    /**
     * Called when edit action is selected for an income item.
     */
    fun onEditIncome(income: IncomeObject)
    
    /**
     * Called when delete action is selected for an income item.
     */
    fun onDeleteIncome(income: IncomeObject)
    
    /**
     * Called when edit action is selected for an expenditure item.
     */
    fun onEditExpenditure(expenditure: ExpenditureObject)
    
    /**
     * Called when delete action is selected for an expenditure item.
     */
    fun onDeleteExpenditure(expenditure: ExpenditureObject)
    
    /**
     * Called when edit action is selected for an event item.
     */
    fun onEditEvent(event: EventObject)
    
    /**
     * Called when delete action is selected for an event item.
     */
    fun onDeleteEvent(event: EventObject)
}
