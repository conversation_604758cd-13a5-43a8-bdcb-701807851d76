package ir.rahavardit.ariel.ui.home

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.PopupMenu
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.model.IncomeObject
import java.text.NumberFormat
import java.util.Locale

class IncomeAdapter(
    private var incomeList: List<IncomeObject>,
    private val actionListener: ItemActionListener? = null
) : RecyclerView.Adapter<IncomeAdapter.IncomeViewHolder>() {

    class IncomeViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val textShortUuid: TextView = itemView.findViewById(R.id.text_short_uuid)
        val textTitle: TextView = itemView.findViewById(R.id.text_title)
        val textAmount: TextView = itemView.findViewById(R.id.text_amount)
        val textBank: TextView = itemView.findViewById(R.id.text_bank)
        val textCategory: TextView = itemView.findViewById(R.id.text_category)
        val textTags: TextView = itemView.findViewById(R.id.text_tags)
        val textCreated: TextView = itemView.findViewById(R.id.text_created)
        val btnMenu: ImageButton = itemView.findViewById(R.id.btn_menu)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): IncomeViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_income, parent, false)
        return IncomeViewHolder(view)
    }

    override fun onBindViewHolder(holder: IncomeViewHolder, position: Int) {
        val income = incomeList[position]

        holder.textShortUuid.text = income.shortUuid
        holder.textBank.text = income.bankInfo.title
        holder.textCreated.text = income.slashedDatePersian

        // Handle title - hide if empty or null
        if (!income.title.isNullOrEmpty()) {
            holder.textTitle.text = income.title
            holder.textTitle.visibility = android.view.View.VISIBLE
        } else {
            holder.textTitle.visibility = android.view.View.GONE
        }

        holder.textCategory.text = "دسته‌بندی: ${income.categoryInfo.title}"

        // Handle tags - hide if empty
        if (income.tagsNames.isNotEmpty()) {
            holder.textTags.text = "برچسب‌ها: ${income.tagsNames.joinToString(", ")}"
            holder.textTags.visibility = android.view.View.VISIBLE
        } else {
            holder.textTags.visibility = android.view.View.GONE
        }

        // Format amount with Persian number formatting
        val numberFormat = NumberFormat.getNumberInstance(Locale("fa", "IR"))
        holder.textAmount.text = "${numberFormat.format(income.amount)} تومان"

        // Setup menu button
        holder.btnMenu.setOnClickListener { view ->
            showPopupMenu(view, income)
        }
    }

    private fun showPopupMenu(view: View, income: IncomeObject) {
        val popupMenu = PopupMenu(view.context, view)
        popupMenu.menuInflater.inflate(R.menu.menu_item_actions, popupMenu.menu)

        popupMenu.setOnMenuItemClickListener { menuItem ->
            when (menuItem.itemId) {
                R.id.action_edit -> {
                    actionListener?.onEditIncome(income)
                    true
                }
                R.id.action_delete -> {
                    actionListener?.onDeleteIncome(income)
                    true
                }
                else -> false
            }
        }

        popupMenu.show()
    }

    override fun getItemCount(): Int = incomeList.size

    fun updateData(newIncomeList: List<IncomeObject>) {
        incomeList = newIncomeList
        notifyDataSetChanged()
    }
}
